import { PermissionWatcher } from './watcher';
import { PermissionValidator } from './validator';
import { PermissionScanner } from './scanner';
import { PermissionSyncer } from './syncer';
import { ReportGenerator } from './reporter';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 開發工具整合
 * 提供開發過程中的權限管理工具
 */
export class PermissionDevTools {
  private watcher: PermissionWatcher;
  private validator: PermissionValidator;
  private scanner: PermissionScanner;
  private syncer: PermissionSyncer;
  private reporter: ReportGenerator;

  constructor() {
    this.scanner = new PermissionScanner();
    this.syncer = new PermissionSyncer();
    this.reporter = new ReportGenerator();
    this.validator = new PermissionValidator();
    this.watcher = new PermissionWatcher({
      autoSync: false, // 開發模式下不自動同步
      verbose: true
    });
  }

  /**
   * 啟動開發模式
   */
  async startDevMode(): Promise<void> {
    console.log('🚀 啟動權限開發模式...');

    // 1. 初始掃描和驗證
    await this.performInitialCheck();

    // 2. 啟動檔案監控
    await this.watcher.start();

    // 3. 設置優雅關閉
    this.setupGracefulShutdown();

    console.log('✅ 權限開發模式已啟動');
    console.log('💡 提示:');
    console.log('  - 檔案變更時會自動檢測權限變更');
    console.log('  - 使用 Ctrl+C 停止監控');
    console.log('  - 執行 pnpm db:sync-perms 同步權限到資料庫');
  }

  /**
   * 執行初始檢查
   */
  private async performInitialCheck(): Promise<void> {
    console.log('🔍 執行初始權限檢查...');

    try {
      // 掃描權限
      const scanResult = await this.scanner.scan(true);
      
      // 驗證一致性
      const validationResult = await this.validator.validate(scanResult.permissions);

      // 檢查是否需要同步
      const syncPreview = await this.syncer.sync(scanResult.permissions, {
        dryRun: true
      });

      if (syncPreview.created > 0 || syncPreview.updated > 0 || syncPreview.deprecated > 0) {
        console.log('⚠️  檢測到權限變更需要同步:');
        console.log(`   新增: ${syncPreview.created} 個`);
        console.log(`   更新: ${syncPreview.updated} 個`);
        console.log(`   廢棄: ${syncPreview.deprecated} 個`);
        console.log('💡 執行 pnpm db:sync-perms 進行同步');
      } else {
        console.log('✅ 權限已同步');
      }

      // 生成開發報告
      await this.generateDevReport(scanResult, validationResult, syncPreview);

    } catch (error) {
      console.error('❌ 初始檢查失敗:', error.message);
    }
  }

  /**
   * 生成開發報告
   */
  private async generateDevReport(
    scanResult: any,
    validationResult: any,
    syncPreview: any
  ): Promise<void> {
    const reportPath = path.join(process.cwd(), 'reports', 'permission-dev-report.json');
    
    const devReport = {
      timestamp: new Date().toISOString(),
      type: 'dev-mode',
      scanResult,
      validationResult,
      syncPreview,
      recommendations: this.generateRecommendations(validationResult, syncPreview)
    };

    // 確保報告目錄存在
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    fs.writeFileSync(reportPath, JSON.stringify(devReport, null, 2));
    console.log(`📊 開發報告已生成: ${reportPath}`);
  }

  /**
   * 生成建議
   */
  private generateRecommendations(validationResult: any, syncPreview: any): string[] {
    const recommendations: string[] = [];

    // 同步建議
    if (syncPreview.created > 0 || syncPreview.updated > 0 || syncPreview.deprecated > 0) {
      recommendations.push('執行 pnpm db:sync-perms 同步權限到資料庫');
    }

    // 驗證建議
    if (validationResult.errors.length > 0) {
      recommendations.push('修復權限一致性錯誤以確保系統穩定性');
    }

    if (validationResult.warnings.length > 0) {
      recommendations.push('考慮處理權限警告以提升程式碼品質');
    }

    // 硬編碼建議
    const hardcodedWarnings = validationResult.warnings.filter(
      (w: any) => w.type === 'HARDCODED_PERMISSION'
    );
    if (hardcodedWarnings.length > 0) {
      recommendations.push('使用 @horizai/permissions 常數替代硬編碼權限字串');
    }

    return recommendations;
  }

  /**
   * 設置優雅關閉
   */
  private setupGracefulShutdown(): void {
    const shutdown = async () => {
      console.log('\n🛑 正在關閉權限開發模式...');
      await this.watcher.stop();
      process.exit(0);
    };

    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
  }

  /**
   * 快速權限檢查（用於 Git hooks 等）
   */
  async quickCheck(): Promise<boolean> {
    console.log('⚡ 執行快速權限檢查...');

    try {
      // 掃描權限
      const scanResult = await this.scanner.scan(true);
      
      // 檢查是否需要同步
      const syncPreview = await this.syncer.sync(scanResult.permissions, {
        dryRun: true
      });

      const hasChanges = syncPreview.created > 0 || syncPreview.updated > 0 || syncPreview.deprecated > 0;

      if (hasChanges) {
        console.log('❌ 檢測到未同步的權限變更');
        console.log(`   新增: ${syncPreview.created} 個`);
        console.log(`   更新: ${syncPreview.updated} 個`);
        console.log(`   廢棄: ${syncPreview.deprecated} 個`);
        console.log('💡 請執行 pnpm db:sync-perms 進行同步');
        return false;
      }

      console.log('✅ 權限檢查通過');
      return true;

    } catch (error) {
      console.error('❌ 快速檢查失敗:', error.message);
      return false;
    }
  }

  /**
   * 生成權限使用統計
   */
  async generateUsageStats(): Promise<void> {
    console.log('📊 生成權限使用統計...');

    try {
      const scanResult = await this.scanner.scan(true);
      const permissions = scanResult.permissions;

      // 統計分析
      const stats = {
        total: permissions.length,
        byScope: this.groupBy(permissions, 'scope'),
        byCategory: this.groupBy(permissions, 'category'),
        bySubject: this.groupBy(permissions, 'subject'),
        byAction: this.groupBy(permissions, 'action'),
        byZone: this.groupBy(permissions, 'zone'),
        fileDistribution: this.analyzeFileDistribution(permissions)
      };

      // 輸出統計
      console.log('\n📈 權限使用統計:');
      console.log(`總權限數: ${stats.total}`);
      
      console.log('\n按範圍分布:');
      Object.entries(stats.byScope).forEach(([scope, count]) => {
        console.log(`  ${scope}: ${count}`);
      });

      console.log('\n按類別分布:');
      Object.entries(stats.byCategory).forEach(([category, count]) => {
        console.log(`  ${category}: ${count}`);
      });

      console.log('\n檔案分布:');
      stats.fileDistribution.slice(0, 10).forEach(([file, count]) => {
        console.log(`  ${path.basename(file)}: ${count}`);
      });

      // 保存統計報告
      const statsPath = path.join(process.cwd(), 'reports', 'permission-usage-stats.json');
      fs.writeFileSync(statsPath, JSON.stringify(stats, null, 2));
      console.log(`\n📊 統計報告已保存: ${statsPath}`);

    } catch (error) {
      console.error('❌ 統計生成失敗:', error.message);
    }
  }

  /**
   * 分組統計
   */
  private groupBy(items: any[], key: string): Record<string, number> {
    return items.reduce((acc, item) => {
      const value = item[key] || 'unknown';
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * 分析檔案分布
   */
  private analyzeFileDistribution(permissions: any[]): [string, number][] {
    const fileCount = this.groupBy(permissions, 'filePath');
    return Object.entries(fileCount)
      .sort(([, a], [, b]) => (b as number) - (a as number));
  }
}
