{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npx nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node --no-deprecation dist/main.js", "start:dev": "npx ts-node scripts/dev-start.ts", "start:debug": "npx nest start --debug --watch", "start:prod": "node --no-deprecation dist/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:auto-fix": "pnpm run db:status && pnpm run db:apply && pnpm prisma generate", "db:quick-reset": "pnpm prisma migrate reset --force && pnpm prisma generate", "port:check": "ts-node scripts/port-manager.ts", "db:seed": "ts-node prisma/seed.ts", "generate": "prisma generate", "postinstall": "prisma generate --schema prisma/schema.prisma || echo 'skip prisma generate'", "migrate:reset": "cross-env SEED_FULL_DATA=true prisma migrate reset", "db:migrate": "ts-node scripts/migrate.ts", "db:status": "ts-node scripts/migrate.ts status", "db:create": "ts-node scripts/migrate.ts create", "db:apply": "ts-node scripts/migrate.ts apply", "db:reset": "ts-node scripts/migrate.ts reset --force", "db:rollback": "ts-node scripts/migrate.ts rollback", "db:check-env": "ts-node scripts/check-env.ts", "db:sync-env": "ts-node scripts/check-env.ts --fix", "db:full-check": "ts-node scripts/check-env.ts --full", "db:export": "ts-node scripts/migrate.ts export", "db:import": "ts-node scripts/migrate.ts import", "db:pull": "prisma db pull --schema=prisma/schema.prisma", "test:perm": "jest --config jest.config.js", "migration": "ts-node scripts/migration/cli.ts", "migration:status": "ts-node scripts/migration/cli.ts status", "migration:list": "ts-node scripts/migration/cli.ts list", "migration:migrate": "ts-node scripts/migration/cli.ts migrate", "migration:quick": "ts-node scripts/migration/cli.ts quick-migrate", "migration:health": "ts-node scripts/migration/cli.ts health", "migration:users": "ts-node scripts/migration/cli.ts quick-migrate --type users-only", "migration:permissions": "ts-node scripts/migration/cli.ts quick-migrate --type permissions-only", "migration:full": "ts-node scripts/migration/cli.ts quick-migrate --type full", "db:studio": "npx prisma studio", "db:sync-perms": "npx ts-node scripts/permission-sync/index.ts", "db:sync-perms:force": "npx ts-node scripts/permission-sync/index.ts --force", "db:scan-perms": "npx ts-node scripts/permission-sync/cli.ts scan", "db:validate-perms": "npx ts-node scripts/permission-sync/cli.ts validate", "db:perms-dev": "npx ts-node scripts/permission-sync/cli.ts dev", "db:perms-check": "npx ts-node scripts/permission-sync/cli.ts check", "db:perms-stats": "npx ts-node scripts/permission-sync/cli.ts stats", "docs:generate": "npx compodoc -p tsconfig.json -s", "docs:generate:no-serve": "npx compodoc -p tsconfig.json"}, "prisma": {"schema": "prisma/schema.prisma", "seed": "ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.1", "@nestjs/testing": "^11.0.2", "@types/cheerio": "^1.0.0", "@types/cookie-parser": "^1.4.8", "@types/date-fns": "^2.6.3", "@types/jest": "^29.5.14", "@types/node": "^20.11.28", "@types/nodemailer": "^6.4.17", "@types/passport-google-oauth20": "^2.0.16", "@types/supertest": "^6.0.3", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.0.0", "prisma": "^6.6.0", "rimraf": "^6.0.1", "style-loader": "^4.0.0", "supertest": "^6.3.3", "swagger-ui-express": "^5.0.1", "ts-jest": "^29.1.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@casl/ability": "^6.7.3", "@casl/prisma": "^1.5.1", "@google/generative-ai": "^0.24.1", "@line/bot-sdk": "^9.9.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.2", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.2", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.2", "@nestjs/platform-socket.io": "^11.0.2", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.2.0", "@nestjs/websockets": "^11.0.2", "@prisma/client": "^6.8.0", "@sendgrid/mail": "^8.1.5", "@types/bcryptjs": "^3.0.0", "@types/express": "^4.17.21", "@types/multer": "^1.4.12", "@types/passport-jwt": "^4.0.1", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "chalk": "^4.1.2", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "commander": "^14.0.0", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "express": "^5.0.1", "express-basic-auth": "^1.2.1", "multer": "1.4.5-lts.2", "nestjs-cls": "^6.0.1", "nodemailer": "^7.0.3", "openai": "^4.103.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-line-auth": "^0.2.9", "passport-local": "^1.0.0", "puppeteer": "^24.9.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.2", "sharp": "^0.34.2", "socket.io": "^4.8.1", "swagger-ui-dist": "^5.24.0", "ts-morph": "^26.0.0", "uuid": "^11.1.0"}}